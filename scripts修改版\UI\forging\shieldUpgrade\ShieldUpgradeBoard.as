package UI.forging.shieldUpgrade
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.shield.ShieldData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.external.ExternalInterface;
   import flash.text.TextField;
   
   public class ShieldUpgradeBoard extends NormalUI
   {
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var skillGrip:ItemsGrid = new ItemsGrid();
      
      public var nowData:ShieldData = null;
      
      private var mustBoxSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var gripTag:Sprite;
      
      private var nameTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;

      public function ShieldUpgradeBoard()
      {
         ExternalInterface.addCallback("shield_EditCheating",Shield_EditCheating);
         super();
         addChild(this.btn);
         addChild(this.mustBox);
         addChild(this.skillGrip);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.skillGrip);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp","gripTag","nameTxt","infoTxt","beforeTxt","nextTxt"];
         super.setImg(img0);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("护盾编辑");
         this.skillGrip.setImgToEquipGrip();
         NormalUICtrl.setTag(this.skillGrip,this.gripTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"shield");
         this.showOneShieldDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:ShieldData) : void
      {
         if(visible)
         {
            this.showOneShieldDataAndPan(da0);
         }
      }
      
      private function showOneShieldDataAndPan(da0:ShieldData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要进阶的护盾。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneShieldData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneShieldData(da0:ShieldData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var d0:HeroSkillDefine = da0.shieldDefine.getSkillDefine();
         var next_d0:HeroSkillDefine = da0.shieldDefine.getNextSkillDefine();
         if(Boolean(d0))
         {
            this.skillGrip.inData_equip(da0);
            this.skillGrip.setNumText("");
            this.nameTxt.text = d0.cnName;
            this.infoTxt.text = d0.getDescriptionNoActiveCd();
            this.beforeTxt.htmlText = ComMethod.color("当前第" + d0.lv + "级","#FF9900") + "\n" + d0.getNowChangeText();
            if(!next_d0)
            {
               this.mustBox.setShowState(false);
               this.btn.actived = true;
               this.nextTxt.htmlText = ComMethod.color("已进阶至最高等级了","#FF9900");
            }
            else
            {
               this.nextTxt.htmlText = ComMethod.color("进阶后第" + next_d0.lv + "级","#FF9900") + "\n" + next_d0.getNowChangeText();
               must_d0 = da0.getUpradeMust();
               bb0 = this.mustBox.inData(must_d0);
               this.btn.actived = true;
            }
         }
         else
         {
            this.showNone();
         }
      }
      
      private function showNone() : void
      {
         this.skillGrip.clearData();
         this.nameTxt.text = "";
         this.infoTxt.text = "";
         this.beforeTxt.text = "";
         this.nextTxt.text = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowData))
         {
            this.afterEdit();
         }
      }
      
      private function afterEdit() : void
      {
         Gaming.uiGroup.alertBox.textInput.showTextInput("=== 护盾编辑 第1页 ===\n等级[1*数量]     强化[2*数量]\n名称[3*文本]     品质[4*英文]\n护盾强度[5*小数] 充能时间[6*数量]\n延迟时间[7*数量] 最大等级[8*数量]\n\n下一页[9] 保存[0]\n品质: white,green,blue,purple,orange,red\n护盾强度如: 0.7=70%, 0.25=25%","",this.Shield_EditCheating);
      }

      public function Shield_EditCheating(str0:String) : void
      {
         var TextArray:Array = str0.split("*");
         
         if(TextArray.length < 2)
         {
            Gaming.uiGroup.alertBox.showError("格式错误！请使用: 数字*值");
            return;
         }
         
         var command:String = String(TextArray[0]);
         var value:String = String(TextArray[1]);
         var numValue:Number = Number(TextArray[1]);

         Gaming.uiGroup.alertBox.showSuccess("接收到指令: " + str0);
         
         if(Boolean(this.nowData))
         {
            var success:Boolean = false;
            
            if(command == "1")
            {
               try {
                  this.nowData.save.itemsLevel = numValue;
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置等级: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "2")
            {
               try {
                  this.nowData.save.strengthenLv = numValue;
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置强化: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "3")
            {
               try {
                  this.nowData.save.cnName = value;
                  this.nowData.shieldDefine.cnName = value;
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置名称: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "4")
            {
               try {
                  this.nowData.save.color = value;
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置品质: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "5")
            {
               try {
                  this.nowData.shieldDefine.stren = numValue;
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置护盾强度: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "6")
            {
               try {
                  this.nowData.shieldDefine.cd = numValue;
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置充能时间: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "7")
            {
               try {
                  this.nowData.shieldDefine.delay = numValue;
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置延迟时间: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "8")
            {
               try {
                  this.nowData.shieldDefine.maxLv = numValue;
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置最大等级: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "9")
            {
               Gaming.uiGroup.alertBox.textInput.showTextInput("=== 护盾编辑 第2页 ===\n进化等级[1*数量] 强化数量[2*数量]\n最大强化[3*数量] 数量[4*数量]\n锁定状态[5*0或1] 新装备[6*0或1]\n技能倍率[7*小数] 技能持续[8*小数]\n\n技能页[99] 返回[9] 保存[0]\n技能倍率如: 0.95, 技能持续如: 0.25秒","",this.Shield_EditCheating);
               return;
            }
            else if(command == "0")
            {
               try {
                  // 保存修改到存档数据
                  this.nowData.save.inData_byObj(this.nowData.save);
                  
                  // 刷新装备数据
                  this.nowData.inData_bySave(this.nowData.save, this.nowData.normalPlayerData, null);
                  
                  // 强制刷新装备属性合并
                  if(this.nowData.placeType == "wear")
                  {
                     this.nowData.normalPlayerData.fleshAllByEquip();
                  }
                  
                  // 触发游戏保存
                  UIOrder.save(true, false, false, null, null, false, true);
                  
                  Gaming.uiGroup.alertBox.showSuccess("护盾属性编辑成功！数据已保存到存档！");
               } catch(e:Error) {
                  Gaming.uiGroup.alertBox.showError("保存失败: " + e.message);
               }
               return;
            }
            
            // 第2页的功能处理
            if(command == "1" && str0.indexOf("进化等级") > -1)
            {
               try {
                  this.nowData.save.evoLv = numValue;
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置进化等级: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "7" && str0.indexOf("技能倍率") > -1)
            {
               try {
                  var skillDefine:Object = this.nowData.shieldDefine.getSkillDefine();
                  if(skillDefine) {
                     skillDefine.mul = numValue;
                  }
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置技能倍率: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "8" && str0.indexOf("技能持续") > -1)
            {
               try {
                  var skillDefine2:Object = this.nowData.shieldDefine.getSkillDefine();
                  if(skillDefine2) {
                     skillDefine2.duration = numValue;
                  }
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置技能持续时间: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "99")
            {
               Gaming.uiGroup.alertBox.textInput.showTextInput("=== 护盾技能编辑 第3页 ===\n技能倍率[1*小数] 技能持续[2*小数]\n技能范围[3*小数] 技能冷却[4*小数]\n技能等级[5*数量] 技能概率[6*小数]\n护盾强度[7*小数] 充能时间[8*数量]\n\n返回[9] 保存[0]","",this.Shield_EditCheating);
               return;
            }
            // 第3页技能编辑功能
            else if(command == "1" && str0.indexOf("技能倍率") > -1)
            {
               try {
                  var skillDefine:Object = this.nowData.shieldDefine.getSkillDefine();
                  if(skillDefine) {
                     skillDefine.mul = numValue;
                  }
                  // 同时修改护盾定义中的相关属性
                  if(this.nowData.shieldDefine.hasOwnProperty("mul")) {
                     this.nowData.shieldDefine.mul = numValue;
                  }
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置技能倍率: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "2" && str0.indexOf("技能持续") > -1)
            {
               try {
                  var skillDefine3:Object = this.nowData.shieldDefine.getSkillDefine();
                  if(skillDefine3) {
                     skillDefine3.duration = numValue;
                  }
                  // 同时修改护盾定义中的相关属性
                  if(this.nowData.shieldDefine.hasOwnProperty("duration")) {
                     this.nowData.shieldDefine.duration = numValue;
                  }
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置技能持续时间: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "3" && str0.indexOf("技能范围") > -1)
            {
               try {
                  var skillDefine4:Object = this.nowData.shieldDefine.getSkillDefine();
                  if(skillDefine4) {
                     skillDefine4.range = numValue;
                  }
                  // 同时修改护盾定义中的相关属性
                  if(this.nowData.shieldDefine.hasOwnProperty("range")) {
                     this.nowData.shieldDefine.range = numValue;
                  }
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置技能范围: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "4" && str0.indexOf("技能冷却") > -1)
            {
               try {
                  var skillDefine5:Object = this.nowData.shieldDefine.getSkillDefine();
                  if(skillDefine5) {
                     skillDefine5.cd = numValue;
                  }
                  // 护盾的冷却时间就是充能时间
                  this.nowData.shieldDefine.cd = numValue;
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置技能冷却: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "5" && str0.indexOf("技能等级") > -1)
            {
               try {
                  var skillDefine6:Object = this.nowData.shieldDefine.getSkillDefine();
                  if(skillDefine6) {
                     skillDefine6.lv = numValue;
                  }
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置技能等级: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "6" && str0.indexOf("技能概率") > -1)
            {
               try {
                  var skillDefine7:Object = this.nowData.shieldDefine.getSkillDefine();
                  if(skillDefine7 && skillDefine7.effectProArr) {
                     skillDefine7.effectProArr[0] = numValue;
                  }
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置技能概率: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "7" && str0.indexOf("护盾强度") > -1)
            {
               try {
                  // 护盾强度是护盾定义的核心属性
                  this.nowData.shieldDefine.stren = numValue;
                  // 同时修改技能定义中的相关属性
                  var skillDefine8:Object = this.nowData.shieldDefine.getSkillDefine();
                  if(skillDefine8 && skillDefine8.hasOwnProperty("stren")) {
                     skillDefine8.stren = numValue;
                  }
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置护盾强度: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "8" && str0.indexOf("充能时间") > -1)
            {
               try {
                  // 充能时间是护盾定义的核心属性
                  this.nowData.shieldDefine.cd = numValue;
                  // 同时修改技能定义中的相关属性
                  var skillDefine9:Object = this.nowData.shieldDefine.getSkillDefine();
                  if(skillDefine9 && skillDefine9.hasOwnProperty("cd")) {
                     skillDefine9.cd = numValue;
                  }
                  success = true;
               } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置充能时间: " + value + (success ? " [成功]" : " [失败]"));
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("未知指令: " + command);
               return;
            }
            
            this.showOneShieldData(this.nowData);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("护盾数据不存在！");
         }
      }
   }
}