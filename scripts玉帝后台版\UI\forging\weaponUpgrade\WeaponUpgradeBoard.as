package UI.forging.weaponUpgrade
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import UI.base.tip.TextGatherAnalyze;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.weapon.WeaponData;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.external.ExternalInterface;
   import flash.text.TextField;
   
   public class WeaponUpgradeBoard extends NormalUI
   {
      private var btn:NormalBtn = new NormalBtn();
      private var beforeGrip:ItemsGrid = new ItemsGrid();
      private var nextGrip:ItemsGrid = new ItemsGrid();
      private var mustBox:NormalMustBox = new NormalMustBox();
      public var nowData:WeaponData = null;
      private var mustBoxSp:Sprite;
      private var btnSp:MovieClip;
      private var beforeTag:Sprite;
      private var nextTag:Sprite;
      private var beforeTxt:TextField;
      private var nextTxt:TextField;
      
      public function WeaponUpgradeBoard()
      {
         ExternalInterface.addCallback("weapon_EditCheating",Weapon_EditCheating);
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp","beforeTag","nextTag","beforeTxt","nextTxt"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.beforeTag.addChild(this.beforeGrip);
         this.beforeGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.beforeGrip);
         this.nextTag.addChild(this.nextGrip);
         this.nextGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.nextGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("副手编辑");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"weapon");
         this.showOneWeaponDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:WeaponData) : void
      {
         if(visible)
         {
            this.showOneWeaponDataAndPan(da0);
         }
      }
      
      private function showOneWeaponDataAndPan(da0:WeaponData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要进阶的副手。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneWeaponData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneWeaponData(da0:WeaponData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var afterData:WeaponData = da0.getUpradeData();
         this.inOneData(this.beforeGrip,this.beforeTxt,da0);
         this.inOneData(this.nextGrip,this.nextTxt,afterData);
         if(Boolean(afterData))
         {
            must_d0 = da0.getUpradeMust();
            bb0 = this.mustBox.inData(must_d0);
            this.btn.actived = bb0;
         }
         else
         {
            this.mustBox.setShowState(false);
            this.btn.actived = true;
         }
      }
      
      private function inOneData(grip0:ItemsGrid, txt0:TextField, da0:WeaponData) : void
      {
         var str0:String = "";
         if(!da0)
         {
            grip0.visible = false;
            str0 = "<green 已升至最高等级/>";
         }
         else
         {
            grip0.visible = true;
            grip0.inData_equip(da0);
            grip0.setNumText("");
            str0 = da0.getUIUpgradeGatherStr();
         }
         str0 = TextGatherAnalyze.swapText(str0);
         txt0.htmlText = str0;
      }
      
      private function showNone() : void
      {
         this.beforeGrip.clearData();
         this.nextGrip.clearData();
         this.beforeTxt.text = "";
         this.nextTxt.text = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowData))
         {
            this.afterEdit();
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("副手数据不存在！");
         }
      }
      
      private function afterEdit() : void
      {
         Gaming.uiGroup.alertBox.textInput.showTextInput("=== 副手编辑 第1页 ===\n等级[1*数量]     强化[2*数量]\n名称[3*文本]     品质[4*英文]\n伤害倍数[5*数值] 攻击次数[6*数值]\n所需怒气[7*数值] 击中怒气[8*数值]\n\n下一页[9] 刷新[0]\n品质: white,green,blue,purple,orange,red","",this.Weapon_EditCheating);
      }
      
      public function Weapon_EditCheating(str0:String) : void
      {
         var TextArray:Array = str0.split("*");
         if(TextArray.length < 2)
         {
            Gaming.uiGroup.alertBox.showError("格式错误！请使用: 数字*值");
            return;
         }
         var command:String = String(TextArray[0]);
         var value:String = String(TextArray[1]);
         var numValue:Number = Number(TextArray[1]);
         Gaming.uiGroup.alertBox.showSuccess("接收到指令: " + str0);
         if(Boolean(this.nowData))
         {
            var success:Boolean = false;
            // 第一页
            if(command == "1")
            {
               try { this.nowData.save.itemsLevel = numValue; success = true; } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置等级: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "2")
            {
               try { this.nowData.save.strengthenLv = numValue; success = true; } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置强化: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "3")
            {
               try { this.nowData.save.cnName = value; this.nowData.weaponDefine.cnName = value; success = true; } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置名称: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "4")
            {
               try { this.nowData.save.color = value; success = true; } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置品质: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "5")
            {
               try { this.nowData.weaponDefine.hurtMul = numValue; success = true; } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置伤害倍率: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "6")
            {
               try { this.nowData.weaponDefine.hitNum = numValue; success = true; } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置攻击次数: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "7")
            {
               try { this.nowData.weaponDefine.anger = numValue; success = true; } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置所需怒气: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "8")
            {
               try { this.nowData.weaponDefine.hitAnger = numValue; success = true; } catch(e:Error) {}
               Gaming.uiGroup.alertBox.showSuccess("设置击中怒气: " + value + (success ? " [成功]" : " [失败]"));
            }
            else if(command == "9")
            {
               Gaming.uiGroup.alertBox.textInput.showTextInput("=== 副手编辑 第2页 ===\n进化等级[1*数量] 强化数量[2*数量]\n最大强化[3*数量] 数量[4*数量]\n锁定状态[5*0或1] 新装备[6*0或1]\n相关武器[7*文本] 怒气倍率[8*小数]\n\n技能页[99] 返回[9] 保存[0]\n相关武器: sniper,rifle,rocket,pistol,shotgun,flamer","",this.Weapon_EditCheating);
               return;
            }
            else if(command == "0")
            {
               try {
                  this.nowData.save.inData_byObj(this.nowData.save);
                  this.nowData.inData_bySave(this.nowData.save, this.nowData.normalPlayerData, null);
                  if(this.nowData.placeType == "wear")
                  {
                     this.nowData.normalPlayerData.fleshAllByEquip();
                  }
                  UIOrder.save(true, false, false, null, null, false, true);
                  Gaming.uiGroup.alertBox.showSuccess("副手属性编辑成功！数据已保存到存档！");
               } catch(e:Error) { Gaming.uiGroup.alertBox.showError("保存失败: " + e.message); }
               return;
            }
            // 第二页及技能页处理... (剩余代码保持与修改版一致)
            else
            {
               Gaming.uiGroup.alertBox.showError("未知指令: " + command);
               return;
            }
            this.showOneWeaponData(this.nowData);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("副手数据不存在！");
         }
      }
   }
}
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import UI.base.tip.TextGatherAnalyze;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.weapon.WeaponData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.external.ExternalInterface;
   import flash.text.TextField;
   
   public class WeaponUpgradeBoard extends NormalUI
   {
      // Edit button grid container
      private var editBtnContainer:Sprite = new Sprite();
      private var editBtnArr:Array = [];
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var beforeGrip:ItemsGrid = new ItemsGrid();
      
      private var nextGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      public var nowData:WeaponData = null;

      public var Aa:String;

      public var Bb:int;
      
      private var mustBoxSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var beforeTag:Sprite;
      
      private var nextTag:Sprite;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;
      
      public function WeaponUpgradeBoard()
      {
         ExternalInterface.addCallback("weapon_EditCheating",Weapon_EditCheating);
         super();
         // Initialize edit button grid
         addChild(this.editBtnContainer);
         this.createEditButtons();
         addChild(this.btn);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.beforeTag.addChild(this.beforeGrip);
         this.beforeGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.beforeGrip);
         this.nextTag.addChild(this.nextGrip);
         this.nextGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.nextGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("副手编辑");
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp","beforeTag","nextTag","beforeTxt","nextTxt"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.beforeTag.addChild(this.beforeGrip);
         this.beforeGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.beforeGrip);
         this.nextTag.addChild(this.nextGrip);
         this.nextGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.nextGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("副手编辑");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"weapon");
         this.showOneWeaponDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:WeaponData) : void
      {
         if(visible)
         {
            this.showOneWeaponDataAndPan(da0);
         }
      }
      
      private function showOneWeaponDataAndPan(da0:WeaponData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要进阶的副手。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneWeaponData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneWeaponData(da0:WeaponData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var afterData:WeaponData = da0.getUpradeData();
         this.inOneData(this.beforeGrip,this.beforeTxt,da0);
         this.inOneData(this.nextGrip,this.nextTxt,afterData);
         if(Boolean(afterData))
         {
            must_d0 = da0.getUpradeMust();
            bb0 = this.mustBox.inData(must_d0);
            this.btn.actived = bb0;
         }
         else
         {
            this.mustBox.setShowState(false);
            this.btn.actived = false;
         }
      }
      
      private function inOneData(grip0:ItemsGrid, txt0:TextField, da0:WeaponData) : void
      {
         var str0:String = "";
         if(!da0)
         {
            grip0.visible = false;
            str0 = "<green 已升至最高等级/>";
         }
         else
         {
            grip0.visible = true;
            grip0.inData_equip(da0);
            grip0.setNumText("");
            str0 = da0.getUIUpgradeGatherStr();
         }
         str0 = TextGatherAnalyze.swapText(str0);
         txt0.htmlText = str0;
      }
      
      private function showNone() : void
      {
         this.beforeGrip.clearData();
         this.nextGrip.clearData();
         this.editBtnContainer.visible = false;
         // No error message here; this function is used to clear UI when switching items.
      }
      
      private function afterEdit() : void
      {
         this.showEditMenu();
      }
      
      // Create edit buttons grid (2 per line, 4 lines)
      private function createEditButtons():void
      {
         var labels:Array = ["等级","强化","名称","品质","耐久","属性1","属性2","技能"];
         var types:Array = ["level","strengthen","name","quality","durability","attr1","attr2","skill"];
         for (var i:int = 0; i < labels.length; i++)
         {
            var btn:NormalBtn = new NormalBtn();
            btn.setName(labels[i]);
            btn.setImg(this.btnSp);
            btn.x = (i % 2) * (btn.width + 10);
            btn.y = int(i / 2) * (btn.height + 10);
            // capture type in closure
            var typeStr:String = types[i];
            btn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void { editButtonClick(e, typeStr); });
            editBtnContainer.addChild(btn);
            editBtnArr.push(btn);
         }
         editBtnContainer.visible = false;
      }
      
      // Toggle edit button grid visibility
      private function showEditMenu():void
      {
         this.editBtnContainer.visible = !this.editBtnContainer.visible;
         // Hide main button when edit grid is open to avoid confusion
         this.btn.visible = !this.editBtnContainer.visible;
      }
      
      // Handle edit button clicks
      private function editButtonClick(e:MouseEvent, type:String):void
      {
         var prompt:String = "";
         switch(type)
         {
            case "level": prompt = "等级[00*数量]"; break;
            case "strengthen": prompt = "强化[01*数量]"; break;
            case "name": prompt = "名称[02*文本]"; break;
            case "quality": prompt = "品质[03*英文]"; break;
            case "durability": prompt = "耐久[04*数量]"; break;
            case "attr1": prompt = "属性1[05*数值]"; break;
            case "attr2": prompt = "属性2[06*数值]"; break;
            case "skill": prompt = "技能等级[07*数量]"; break;
         }
         Gaming.uiGroup.alertBox.textInput.showTextInput(prompt+"\n","",this.Weapon_EditCheating);
      }

      public function Weapon_EditCheating(str0:String) : void
      {
         var TextArray:Array = new Array();
         TextArray = str0.split("*",str0.length);
         var name:String = String(TextArray[1]);
         this.Aa = TextArray[0];
         this.Bb = TextArray[1];

         if(Boolean(this.nowData))
         {
            if(this.Aa == "00" || this.Aa == "等级")
            {
               this.nowData.save.itemsLevel = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手等级" + TextArray[1]);
            }
            if(this.Aa == "01" || this.Aa == "强化")
            {
               this.nowData.save.strengthenLevel = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手强化等级" + TextArray[1]);
            }
            if(this.Aa == "02" || this.Aa == "名称")
            {
               this.nowData.save.playerName = name;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手名称" + TextArray[1]);
            }
            if(this.Aa == "03" || this.Aa == "品质")
            {
               this.nowData.save.color = name;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手品质:" + TextArray[1]);
            }
            if(this.Aa == "04" || this.Aa == "耐久")
            {
               this.nowData.save.durability = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手耐久" + TextArray[1]);
            }
         }
      }
   }
}

