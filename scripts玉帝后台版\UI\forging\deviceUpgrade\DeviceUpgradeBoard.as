package UI.forging.deviceUpgrade
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.device.DeviceData;
   import dataAll.equip.device.DeviceSave;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class DeviceUpgradeBoard extends NormalUI
   {
      
      private var copyData:DeviceData;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var skillGrip:ItemsGrid = new ItemsGrid();
      
      public var nowData:DeviceData = null;
      
      private var mustBoxSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var gripTag:Sprite;
      
      private var nameTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;
      
      public function DeviceUpgradeBoard()
      {
         super();
         addChild(this.btn);
         addChild(this.mustBox);
         addChild(this.skillGrip);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.skillGrip);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp","gripTag","nameTxt","infoTxt","beforeTxt","nextTxt"];
         super.setImg(img0);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("编辑装置");
         this.skillGrip.setImgToEquipGrip();
         NormalUICtrl.setTag(this.skillGrip,this.gripTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"device");
         this.showOneDeviceDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:DeviceData) : void
      {
         if(visible)
         {
            this.showOneDeviceDataAndPan(da0);
         }
      }
      
      private function showOneDeviceDataAndPan(da0:DeviceData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要编辑的装置。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneDeviceData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneDeviceData(da0:DeviceData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var d0:HeroSkillDefine = da0.deviceDefine.getSkillDefine();
         var next_d0:HeroSkillDefine = da0.deviceDefine.getNextSkillDefine();
         this.skillGrip.inData_equip(da0);
         this.skillGrip.setNumText("");
         this.nameTxt.text = d0.cnName;
         this.infoTxt.text = d0.getDescriptionNoActiveCd();
         this.beforeTxt.htmlText = ComMethod.color("当前第" + d0.lv + "级","#FF9900") + "\n" + d0.getNowChangeText();
         if(!next_d0)
         {
            this.mustBox.setShowState(false);
            this.btn.actived = true;
            this.nextTxt.htmlText = ComMethod.color("已进阶至最高等级了","#FF9900");
         }
         else
         {
            this.nextTxt.htmlText = ComMethod.color("进阶后第" + next_d0.lv + "级","#FF9900") + "\n" + next_d0.getNowChangeText();
            must_d0 = da0.getUpradeMust();
            bb0 = this.mustBox.inData(must_d0);
            this.btn.actived = true;
         }
      }
      
      private function showNone() : void
      {
         this.skillGrip.clearData();
         this.nameTxt.text = "";
         this.infoTxt.text = "";
         this.beforeTxt.text = "";
         this.nextTxt.text = "";
         this.mustBox.setShowState(false);
         this.btn.actived = true;
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.textInput.showTextInput("进阶等级[00*数值] 复制数据[01*可空] 粘贴数据[02*可空]\n装置数量[03*数值] 获取时间[04*文本] 到期时间[05*文本]\n强化等级[06*数值] 装置代码[07*代码] 图片代码[08*代码]\n","",this.YueYingEdit);
      }
      
      public function YueYingEdit(str0:String) : void
      {
         var s0:DeviceSave = new DeviceSave();
         var YueYing:Array = new Array();
         YueYing = str0.split("*",str0.length);
         s0.inData_byObj(this.nowData.save);
         if(YueYing[0] == "00" || YueYing[0] == "进阶等级")
         {
            s0.itemsLevel = Number(YueYing[1]);
         }
         else if(YueYing[0] == "01" || YueYing[0] == "复制数据")
         {
            this.copyData = this.nowData;
            Gaming.uiGroup.alertBox.showSuccess("复制当前装置数据成功！");
            this.showOneEquipDataAndPan(this.nowData);
         }
         else if(YueYing[0] == "02" || YueYing[0] == "粘贴数据")
         {
            if(this.copyData == null)
            {
               Gaming.uiGroup.alertBox.showSuccess("当前无复制数据！");
            }
            else
            {
               s0.inData_byObj(this.copyData.save);
               Gaming.PG.da.equipBag.addSave(s0);
               Gaming.uiGroup.alertBox.showSuccess("添加装备成功！");
               this.copyData = null;
               this.showOneDeviceDataAndPan(this.nowData);
            }
         }
         else if(YueYing[0] == "03" || YueYing[0] == "装置数量")
         {
            s0.nowNum = YueYing[1];
         }
         else if(YueYing[0] == "04" || YueYing[0] == "获取时间")
         {
            s0.getTime = YueYing[1];
         }
         else if(YueYing[0] == "05" || YueYing[0] == "到期时间")
         {
            s0.severTime = YueYing[1];
         }
         else if(YueYing[0] == "06" || YueYing[0] == "强化等级")
         {
            s0.strengthenLv = Number(YueYing[1]);
         }
         else if(YueYing[0] == "07" || YueYing[0] == "装置代码")
         {
            s0.name = YueYing[1];
         }
         else if(YueYing[0] == "08" || YueYing[0] == "图片代码")
         {
            s0.imgName = YueYing[1];
         }
         this.nowData.save = s0;
         Gaming.uiGroup.alertBox.showSuccess("装置属性编辑成功！");
      }
      
      private function affter_click() : void
      {
         var upgradeData0:DeviceData = this.nowData.getUpradeData();
         if(this.nowData.placeType == "wear" || this.nowData.getNowNum() == 1)
         {
            this.nowData.changeToOneData(upgradeData0);
         }
         else if(this.nowData.placeType == "bag")
         {
            if(this.nowData.getNowNum() > 1 && this.nowData.getSave().itemsLevel >= 2)
            {
               this.nowData.addNowNum(-1);
            }
            this.nowData = Gaming.PG.da.equipBag.addHaveNumData(upgradeData0) as DeviceData;
         }
         this.show();
         Gaming.uiGroup.alertBox.showSuccess("进阶成功！");
      }
   }
}

